#!/usr/bin/env node

/**
 * Sistema de diagnóstico para ATS Dashboard
 * Verifica el estado de Docker, PostgreSQL y la aplicación
 */

import { exec } from 'child_process';
import { promisify } from 'util';
const execAsync = promisify(exec);

async function runCommand(command, description) {
  console.log(`\n🔍 ${description}...`);
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) {
      console.log(`✅ ${description}: OK`);
      console.log(stdout.trim());
    }
    if (stderr && !stderr.includes('Warning')) {
      console.log(`⚠️  ${description}: Warning`);
      console.log(stderr.trim());
    }
    return true;
  } catch (error) {
    console.log(`❌ ${description}: FAILED`);
    console.log(error.message);
    return false;
  }
}

async function checkDockerStatus() {
  console.log('\n=== DOCKER STATUS ===');
  
  const dockerVersion = await runCommand('docker --version', 'Docker Version');
  if (!dockerVersion) return false;
  
  const dockerRunning = await runCommand('docker info', 'Docker Daemon Status');
  if (!dockerRunning) {
    console.log('\n💡 Solución: Reinicia Docker con:');
    console.log('   sudo systemctl restart docker');
    console.log('   # o si usas Docker Desktop:');
    console.log('   sudo service docker restart');
    return false;
  }
  
  await runCommand('docker ps', 'Running Containers');
  await runCommand('docker-compose ps', 'Docker Compose Status');
  
  return true;
}

async function checkDatabase() {
  console.log('\n=== DATABASE STATUS ===');
  
  // Verificar si PostgreSQL está corriendo en Docker
  const pgContainer = await runCommand(
    'docker ps --filter "name=postgres" --format "table {{.Names}}\\t{{.Status}}"',
    'PostgreSQL Container'
  );
  
  if (pgContainer) {
    // Intentar conectar a la base de datos
    await runCommand(
      'docker exec ats-dashboard-guru-postgres pg_isready -U postgres',
      'PostgreSQL Connection Test'
    );
    
    // Verificar tablas
    await runCommand(
      'docker exec ats-dashboard-guru-postgres psql -U postgres -d postgres -c "\\dt"',
      'Database Tables'
    );
    
    // Contar clientes
    await runCommand(
      'docker exec ats-dashboard-guru-postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as client_count FROM clients;"',
      'Client Count'
    );
  }
}

async function checkApplication() {
  console.log('\n=== APPLICATION STATUS ===');
  
  // Verificar si el servidor está corriendo
  await runCommand('curl -s http://localhost:3001/api/clients | head -c 100', 'API Server Response');
  
  // Verificar archivos importantes
  await runCommand('ls -la package.json', 'Package.json');
  await runCommand('ls -la .env*', 'Environment Files');
  await runCommand('ls -la docker-compose.yml', 'Docker Compose File');
}

async function suggestSolutions() {
  console.log('\n=== SOLUCIONES RECOMENDADAS ===');
  
  console.log('\n🔧 Para problemas de Docker:');
  console.log('   1. sudo systemctl restart docker');
  console.log('   2. docker-compose down && docker-compose up -d');
  
  console.log('\n🔧 Para ejecutar sin Docker:');
  console.log('   1. Terminal 1: npm run server');
  console.log('   2. Terminal 2: npm run dev');
  
  console.log('\n🔧 Para problemas de base de datos:');
  console.log('   1. npm run db:init');
  console.log('   2. npm run db:migrate');
  
  console.log('\n🔧 Para limpiar y reiniciar:');
  console.log('   1. docker-compose down -v');
  console.log('   2. docker-compose up -d');
  console.log('   3. npm run db:init');
}

async function main() {
  console.log('🚀 ATS Dashboard - Diagnóstico del Sistema');
  console.log('==========================================');
  
  const dockerOk = await checkDockerStatus();
  
  if (dockerOk) {
    await checkDatabase();
  }
  
  await checkApplication();
  await suggestSolutions();
  
  console.log('\n✨ Diagnóstico completado');
}

main().catch(console.error);
