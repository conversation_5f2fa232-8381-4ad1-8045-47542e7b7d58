import { useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Building,
  MapPin,
  Mail,
  Phone,
  Globe,
  FileEdit,
  Trash2,
  Briefcase,
  Plus,
  MessageSquare,
} from 'lucide-react';
import { createExternalLinkProps } from '@/utils/urlUtils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useClient, useDeleteClient } from '@/hooks/useClients';
import { useToast } from '@/hooks/use-toast';
import { ClientNotesSection } from '@/components/clients/ClientNotesSection';
import { ClientNoteCreator } from '@/components/clients/ClientNoteCreator';

export default function ClientDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [lastAddedNoteId, setLastAddedNoteId] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Obtener datos del cliente
  const { client, isLoading, error } = useClient(id || '');

  // Mutación para eliminar cliente
  const deleteClientMutation = useDeleteClient();

  // Log para verificar los datos del cliente
  console.log('Client details:', { client, isLoading, error });

  // Manejar la eliminación del cliente
  const handleDeleteClient = async () => {
    if (!id) return;

    try {
      await deleteClientMutation.mutateAsync(id);
      toast({
        title: 'Client Deleted',
        description: 'The client has been successfully deleted.',
      });
      navigate('/clients');
    } catch (error) {
      console.error('Error deleting client:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete client. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Renderizar estado del cliente como badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'lead':
        return <Badge className="bg-blue-500">Lead</Badge>;
      case 'former':
        return <Badge variant="outline">Former</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-[calc(100vh-200px)]">
          <p className="text-muted-foreground">Loading client details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !client) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
          <h2 className="text-2xl font-bold mb-2">Client Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The client you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild>
            <Link to="/clients">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Clients
            </Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" asChild>
          <Link to="/clients">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{client.company_name}</h1>
        {renderStatusBadge(client.status)}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="jobs">Jobs</TabsTrigger>
              <TabsTrigger value="contacts">Contacts</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>Client Information</CardTitle>
                  <CardDescription>Basic information about the client</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-sm font-medium mb-1">Company</h3>
                      <p className="text-lg">{client.company_name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Industry</h3>
                      <p className="text-lg">{client.industry || 'N/A'}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Primary Contact</h3>
                      <p className="text-lg">{client.contact_name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Email</h3>
                      <p className="text-lg">
                        <a href={`mailto:${client.email}`} className="text-primary hover:underline">
                          {client.email}
                        </a>
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Phone</h3>
                      <p className="text-lg">
                        {client.phone ? (
                          <a href={`tel:${client.phone}`} className="text-primary hover:underline">
                            {client.phone}
                          </a>
                        ) : (
                          'N/A'
                        )}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Location</h3>
                      <p className="text-lg">{client.location || 'N/A'}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Website</h3>
                      <p className="text-lg">
                        {client.website ? (
                          <a
                            {...createExternalLinkProps(client.website)}
                            className="text-primary hover:underline"
                          >
                            {client.website}
                          </a>
                        ) : (
                          'N/A'
                        )}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">Client Since</h3>
                      <p className="text-lg">
                        {new Date(client.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="jobs">
              <Card>
                <CardHeader>
                  <CardTitle>Jobs</CardTitle>
                  <CardDescription>Jobs associated with this client</CardDescription>
                </CardHeader>
                <CardContent>
                  {client.jobs && client.jobs.length > 0 ? (
                    <div className="space-y-4">
                      {/* Aquí iría la lista de trabajos asociados al cliente */}
                      <p>This client has {client.jobs.length} associated jobs.</p>
                      <Button asChild>
                        <Link to={`/jobs/new?client=${client.id}`}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add New Job
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Briefcase className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Jobs Yet</h3>
                      <p className="text-muted-foreground mb-4">
                        This client doesn't have any jobs associated with it yet.
                      </p>
                      <Button asChild>
                        <Link to={`/jobs/new?client=${client.id}`}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add New Job
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="contacts">
              <Card>
                <CardHeader>
                  <CardTitle>Contacts</CardTitle>
                  <CardDescription>People associated with this client</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Phone className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Contact Management Coming Soon</h3>
                    <p className="text-muted-foreground mb-4">
                      This feature is under development and will be available soon.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes">
              <div className="space-y-6">
                {/* Legacy notes from client.notes field */}
                {client.notes && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileEdit className="h-5 w-5" />
                        Legacy Notes
                      </CardTitle>
                      <CardDescription>
                        Notes stored in the client record (consider moving to timeline notes below)
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="prose max-w-none">
                        <p className="whitespace-pre-wrap">{client.notes}</p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* New timeline-based notes system */}
                <ClientNotesSection clientId={client.id} />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="ghost" asChild>
                <a href={`mailto:${client.email}`}>
                  <Mail className="h-4 w-4 mr-2" />
                  Send Email
                </a>
              </Button>
              {client.phone && (
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <a href={`tel:${client.phone}`}>
                    <Phone className="h-4 w-4 mr-2" />
                    Call
                  </a>
                </Button>
              )}
              {client.website && (
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <a {...createExternalLinkProps(client.website)}>
                    <Globe className="h-4 w-4 mr-2" />
                    Visit Website
                  </a>
                </Button>
              )}
              <Button className="w-full justify-start" variant="ghost" asChild>
                <Link to={`/jobs/new?client=${client.id}`}>
                  <Briefcase className="h-4 w-4 mr-2" />
                  Create Job
                </Link>
              </Button>
            </CardContent>
            <Separator />
            <CardFooter className="flex justify-between p-4">
              <Button variant="outline" asChild>
                <Link to={`/clients/${client.id}/edit`}>
                  <FileEdit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete the client "{client.company_name}" and all associated data.
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteClient} className="bg-destructive text-destructive-foreground">
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardFooter>
          </Card>

          <div className="mt-6">
            <ClientNoteCreator
              clientId={client.id}
              onNoteAdded={(note) => {
                console.log('📝 Note added with ID:', note.id);
                setLastAddedNoteId(note.id);

                // Trigger refresh of the timeline
                setRefreshTrigger(prev => prev + 1);

                // Switch to notes tab and scroll to the new note
                setActiveTab('notes');

                // Scroll to the timeline after a short delay to allow tab switching and refresh
                setTimeout(() => {
                  const timelineElement = document.querySelector('[data-timeline-container]');
                  if (timelineElement) {
                    timelineElement.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }
                }, 300);
              }}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
